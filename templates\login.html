<!DOCTYPE html>
<html lang="cs">
<head>
  <meta charset="UTF-8">
  <title><PERSON><PERSON><PERSON>lášení</title>
  <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body class="login-body">
  <div class="login-container">
    <h1>Vstup pro členy</h1>
    {% if error %}<p class="error">{{ error }}</p>{% endif %}
    <form method="POST" class="login-form">
      <div class="input-container">
        <input type="email" name="email" placeholder="V<PERSON>š email" required class="login-input" autocomplete="off">

        <div class="passcode-label">Váš 6-místný kód:</div>
        <div class="passcode-container">
          <input type="text" name="digit1" maxlength="1" pattern="[0-9]" inputmode="numeric" class="passcode-input" required>
          <input type="text" name="digit2" maxlength="1" pattern="[0-9]" inputmode="numeric" class="passcode-input" required>
          <input type="text" name="digit3" maxlength="1" pattern="[0-9]" inputmode="numeric" class="passcode-input" required>
          <input type="text" name="digit4" maxlength="1" pattern="[0-9]" inputmode="numeric" class="passcode-input" required>
          <input type="text" name="digit5" maxlength="1" pattern="[0-9]" inputmode="numeric" class="passcode-input" required>
          <input type="text" name="digit6" maxlength="1" pattern="[0-9]" inputmode="numeric" class="passcode-input" required>
          <input type="hidden" name="password" id="combined-password">
        </div>

        <div class="submission-indicator">
          <div class="submission-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
          <p>Přihlašování...</p>
        </div>

        <button type="submit" class="login-button">Přihlásit se</button>
      </div>
    </form>
  </div>

  <script>
    // Auto-focus to next input when a digit is entered
    const passcodeInputs = document.querySelectorAll('.passcode-input');
    const combinedPassword = document.getElementById('combined-password');
    const passcodeContainer = document.querySelector('.passcode-container');

    // Check if there's an error message and trigger shake animation
    const errorMessage = document.querySelector('.error');
    if (errorMessage) {
      // Clear all passcode inputs
      passcodeInputs.forEach(input => {
        input.value = '';
      });

      // Trigger shake animation on page load if there's an error
      setTimeout(() => {
        passcodeContainer.classList.add('shake');
        passcodeInputs.forEach(input => {
          input.classList.add('error');
        });

        // Remove shake class after animation completes
        setTimeout(() => {
          passcodeContainer.classList.remove('shake');
        }, 600);

        // Remove error class after a longer delay
        setTimeout(() => {
          passcodeInputs.forEach(input => {
            input.classList.remove('error');
          });
        }, 2000);

        // Focus on the first input after the animation
        setTimeout(() => {
          passcodeInputs[0].focus();
        }, 700);
      }, 100);
    }

    passcodeInputs.forEach((input, index) => {
      // Focus on first input when page loads
      if (index === 0) {
        setTimeout(() => input.focus(), 100);
      }

      // Handle input events
      input.addEventListener('input', function(e) {
        // Only allow numbers
        this.value = this.value.replace(/[^0-9]/g, '');

        // If a digit was entered, move to the next input or submit if last digit
        if (this.value.length === 1) {
          if (index < passcodeInputs.length - 1) {
            passcodeInputs[index + 1].focus();
          } else {
            // This is the last input and a digit was entered
            // Update password and submit form if all digits are filled
            updateCombinedPassword();

            // Check if all inputs have values
            let allFilled = true;
            passcodeInputs.forEach(input => {
              if (input.value.length === 0) {
                allFilled = false;
              }
            });

            if (allFilled) {
              showSubmissionEffectAndSubmit();
            }
          }
        }

        // Update the hidden combined password field
        updateCombinedPassword();
      });

      // Handle backspace
      input.addEventListener('keydown', function(e) {
        if (e.key === 'Backspace' && this.value === '' && index > 0) {
          // If backspace is pressed on an empty input, go back to previous input
          passcodeInputs[index - 1].focus();
        }
      });

      // Handle paste event
      input.addEventListener('paste', function(e) {
        e.preventDefault();
        const pastedData = (e.clipboardData || window.clipboardData).getData('text');
        if (/^\d+$/.test(pastedData)) {
          // If pasted content is all digits, distribute them across inputs
          const digits = pastedData.split('');
          passcodeInputs.forEach((input, i) => {
            if (i < digits.length) {
              input.value = digits[i];
            }
          });

          // Update the combined password
          updateCombinedPassword();

          // Check if we have all 6 digits (either from paste or already filled)
          if (combinedPassword.value.length === 6) {
            showSubmissionEffectAndSubmit();
          } else {
            // Focus on the next empty input or the last one
            let nextEmptyIndex = 0;
            for (let i = 0; i < passcodeInputs.length; i++) {
              if (!passcodeInputs[i].value) {
                nextEmptyIndex = i;
                break;
              }
              nextEmptyIndex = Math.min(digits.length, passcodeInputs.length - 1);
            }
            passcodeInputs[nextEmptyIndex].focus();
          }
        }
      });
    });

    // Update the hidden combined password field
    function updateCombinedPassword() {
      let combined = '';
      passcodeInputs.forEach(input => {
        combined += input.value;
      });
      combinedPassword.value = combined;
    }

    // Show submission animation and submit the form
    function showSubmissionEffectAndSubmit() {
      // Add success animation to all inputs
      passcodeInputs.forEach(input => {
        input.classList.add('success');
        input.setAttribute('readonly', true); // Prevent further editing
      });

      // Show submission indicator
      const submissionIndicator = document.querySelector('.submission-indicator');
      submissionIndicator.classList.add('active');

      // Hide the submit button
      const submitButton = document.querySelector('.login-button');
      submitButton.style.display = 'none';

      // Submit the form after a delay
      setTimeout(() => {
        document.querySelector('.login-form').submit();
      }, 1200);
    }

    // Handle form submission
    document.querySelector('.login-form').addEventListener('submit', function(e) {
      updateCombinedPassword();
      // If the combined password is not 6 digits, prevent submission
      if (combinedPassword.value.length !== 6) {
        e.preventDefault();
        alert('Prosím zadejte 6-místný kód.');
        // Focus on the first empty input
        for (let i = 0; i < passcodeInputs.length; i++) {
          if (!passcodeInputs[i].value) {
            passcodeInputs[i].focus();
            break;
          }
        }
      }
    });
  </script>
</body>
</html>
