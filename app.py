import csv

from flask import Flask, redirect, render_template, request, session, url_for

app = Flask(__name__)
app.secret_key = "your_secret_key"  # Replace with a strong secret


def load_users():
    users = {}
    with open("users.csv", newline="", encoding="utf-8") as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            users[row["email"]] = row["code"]
    return users


@app.route("/", methods=["GET", "POST"])
def login():
    error = None
    success = False
    email_value = ""
    password_value = ""

    if request.method == "POST":
        email = request.form["email"]
        password = request.form["password"]
        users = load_users()
        if email in users and users[email] == password:
            session["user"] = email
            success = True
            email_value = email
            password_value = password
            return render_template(
                "modern-login.html",
                success=True,
                email_value=email_value,
                password_value=password_value,
            )
        else:
            error = "Neplatný email nebo unik<PERSON><PERSON><PERSON><PERSON> kód."
            email_value = email  # Keep email on error

    return render_template(
        "modern-login.html",
        error=error,
        success=success,
        email_value=email_value,
        password_value=password_value,
    )


@app.route("/index")
def index():
    if "user" not in session:
        return redirect(url_for("login"))
    return render_template("modern-index.html")


@app.route("/contact", methods=["POST"])
def contact():
    if "user" not in session:
        return redirect(url_for("login"))
    # Here you would process the contact form
    # For now, we'll just redirect back to the index page
    return redirect(url_for("index"))


@app.route("/logout")
def logout():
    session.clear()
    return redirect(url_for("login"))


if __name__ == "__main__":
    app.run(debug=True)
