import csv

from flask import Flask, redirect, render_template, request, session, url_for

app = Flask(__name__)
app.secret_key = "your_secret_key"  # Replace with a strong secret


def load_users():
    users = {}
    with open("users.csv", newline="", encoding="utf-8") as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            users[row["email"]] = row["code"]
    return users


@app.route("/", methods=["GET", "POST"])
def login():
    error = None
    success = False
    if request.method == "POST":
        email = request.form["email"]
        password = request.form["password"]
        users = load_users()
        if email in users and users[email] == password:
            session["user"] = email
            success = True
            return render_template("modern-login.html", success=True)
        else:
            error = "Neplatný email nebo unikátní kód."
    return render_template("modern-login.html", error=error, success=success)


@app.route("/index")
def index():
    if "user" not in session:
        return redirect(url_for("login"))
    return render_template("modern-index.html")


@app.route("/contact", methods=["POST"])
def contact():
    if "user" not in session:
        return redirect(url_for("login"))
    # Here you would process the contact form
    # For now, we'll just redirect back to the index page
    return redirect(url_for("index"))


@app.route("/logout")
def logout():
    session.clear()
    return redirect(url_for("login"))


if __name__ == "__main__":
    app.run(debug=True)
