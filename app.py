import csv

from flask import Flask, redirect, render_template, request, session, url_for

app = Flask(__name__)
app.secret_key = "your_secret_key"  # Replace with a strong secret


def load_users():
    users = {}
    with open("users.csv", newline="", encoding="utf-8") as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            users[row["email"]] = row["code"]
    return users


@app.route("/", methods=["GET", "POST"])
def login():
    error = None
    if request.method == "POST":
        email = request.form["email"]
        password = request.form["password"]
        users = load_users()
        if email in users and users[email] == password:
            session["user"] = email
            return redirect(url_for("index"))
        else:
            error = "Neplatný email nebo unikátní kód."
    return render_template("login.html", error=error)


@app.route("/index")
def index():
    if "user" not in session:
        return redirect(url_for("login"))
    return render_template("index.html")


@app.route("/logout")
def logout():
    session.clear()
    return redirect(url_for("login"))


if __name__ == "__main__":
    app.run(debug=True)
