body {
  margin: 0;
  font-family: 'Segoe UI', sans-serif;
  background: #111;
  color: #fff;
}

.login-body {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.login-container {
  background: #1e1e1e;
  padding: 3rem;
  border-radius: 15px;
  text-align: center;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.login-container h1 {
  font-size: 2.5rem;
  margin-bottom: 2rem;
}

.login-container input,
.login-container button {
  display: block;
  margin: 1.5rem auto;
  padding: 1rem;
  width: 100%;
  max-width: 400px;
  font-size: 1.2rem;
  border-radius: 8px;
  border: none;
}

.login-container button {
  background: #ff99aa;
  color: white;
  cursor: pointer;
  font-weight: bold;
  font-size: 1.3rem;
  padding: 1.2rem;
  transition: background-color 0.3s;
}

.login-container button:hover {
  background: #ff7a8e;
}

.error {
  color: #ff5555;
}

.dashboard-body {
  padding: 2rem;
  background: #111;
}

.grid-container {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
}

.text-section {
  flex: 1 1 40%;
  max-width: 500px;
}

.text-section h1 {
  font-size: 2.5rem;
}

.image-gallery {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  flex: 1 1 50%;
}

.image-gallery img {
  width: 100%;
  border-radius: 12px;
  object-fit: cover;
}

.logout-btn {
  display: inline-block;
  margin-top: 1rem;
  color: #ccc;
  font-size: 0.9rem;
}
