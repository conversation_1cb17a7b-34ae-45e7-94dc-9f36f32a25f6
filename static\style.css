body {
  margin: 0;
  font-family: 'Segoe UI', sans-serif;
  background: #111;
  color: #fff;
}

.login-body {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.login-container {
  background: #1e1e1e;
  padding: 3rem;
  border-radius: 15px;
  text-align: center;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.login-container h1 {
  font-size: 2.5rem;
  margin-bottom: 2rem;
  color: white;
}

/* Apply box-sizing to all elements */
* {
  box-sizing: border-box;
}

/* Reset button styling */
button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  margin: 0;
  padding: 0;
}

/* Login form styling */
.login-form {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

/* Common styles for inputs and button */
.login-input,
.login-button {
  display: block;
  width: 100%;
  margin: 1.5rem auto;
  padding: 1rem;
  font-size: 1.2rem;
  border-radius: 8px;
  border: none;
  box-sizing: border-box; /* Ensure padding is included in width */
}

/* Button specific styles */
.login-button {
  background: #ff99aa;
  color: white;
  cursor: pointer;
  font-weight: bold;
  font-size: 1.3rem;
  padding: 1.2rem;
  transition: background-color 0.3s;
  width: 100%; /* Explicitly set width */
}

.login-button:hover {
  background: #ff7a8e;
}

.error {
  color: #ff5555;
}

/* Main Website Styles */
:root {
  --primary-color: #ff98a9;
  --secondary-color: #181818;
  --accent-color: #ff98a9;
  --light-color: #f8f8f8;
  --dark-color: #181818;
  --neutral-secondary: #1f1f1f;
  --text-color: #f8f8f8;
  --text-light: #aaaaaa;
  --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  --transition: all 0.3s ease;
}

/* Reset some basic elements */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Base styles */
.main-body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--secondary-color);
  overflow-x: hidden;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
  color: var(--dark-color);
}

p {
  margin-bottom: 1rem;
}

a {
  text-decoration: none;
  color: var(--primary-color);
  transition: var(--transition);
}

a:hover {
  color: var(--accent-color);
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 12px 30px;
  border-radius: 50px;
  text-transform: uppercase;
  font-weight: 700;
  font-size: 14px;
  letter-spacing: 1px;
  transition: var(--transition);
  cursor: pointer;
  border: none;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
  box-shadow: 0 4px 15px rgba(255, 152, 169, 0.3);
}

.btn-primary:hover {
  background-color: var(--primary-color);
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(255, 152, 169, 0.4);
}

/* Header and Navigation */
.main-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: rgba(24, 24, 24, 0.95);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  padding: 15px 0;
  border-bottom: 1px solid rgba(255, 152, 169, 0.2);
}

.main-header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo h2 {
  font-size: 28px;
  color: var(--primary-color);
  margin: 0;
}

.main-nav ul {
  display: flex;
  list-style: none;
}

.main-nav ul li {
  margin-left: 30px;
}

.main-nav ul li a {
  color: var(--light-color);
  font-weight: 600;
  font-size: 16px;
  padding: 10px 0;
  position: relative;
}

.main-nav ul li a::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--primary-color);
  transition: var(--transition);
}

.main-nav ul li a:hover::after {
  width: 100%;
}

.logout-link {
  color: var(--accent-color) !important;
}

.menu-toggle {
  display: none;
  font-size: 24px;
  cursor: pointer;
}

/* Hero Section */
.hero-section {
  height: 100vh;
  background: linear-gradient(rgba(24, 24, 24, 0.85), rgba(31, 31, 31, 0.85)), url('../static/images/strategy1.jpg') center/cover;
  display: flex;
  align-items: center;
  text-align: center;
  color: white;
  padding-top: 80px;
  position: relative;
}

.hero-section::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: var(--primary-color);
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero-content h1 {
  font-size: 48px;
  margin-bottom: 20px;
  color: white;
  font-weight: 700;
}

.hero-content h1 span {
  color: var(--primary-color);
}

.hero-content .lead {
  font-size: 20px;
  margin-bottom: 30px;
}

/* Section Styling */
section {
  padding: 100px 0;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-header h2 {
  font-size: 36px;
  position: relative;
  padding-bottom: 15px;
  color: white;

}

.section-header h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 70px;
  height: 3px;
  background-color: var(--primary-color);
}

.section-header p {
  font-size: 18px;
  color: var(--text-light);
}

/* Services Section */
.services-section {
  background-color: var(--neutral-secondary);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.service-card {
  background-color: var(--secondary-color);
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  text-align: center;
  transition: var(--transition);
  border: 1px solid rgba(255, 152, 169, 0.1);
  color: var(--light-color);
}

.service-card:hover {
  transform: translateY(-10px);
}

.service-icon {
  font-size: 40px;
  color: var(--primary-color);
  margin-bottom: 20px;
}

.service-card h3 {
  font-size: 22px;
  margin-bottom: 15px;
  color: white;

}

/* Team Section */
.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 40px;
}

.team-member {
  background-color: var(--neutral-secondary);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: var(--transition);
  border: 1px solid rgba(255, 152, 169, 0.1);
  color: var(--light-color);
}

.team-member:hover {
  transform: translateY(-10px);
}

.member-image {
  height: 280px;
  overflow: hidden;
}

.member-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.team-member:hover .member-image img {
  transform: scale(1.1);
}

.team-member h3, .team-member p {
  padding: 0 20px;
}

.team-member h3 {
  margin-top: 20px;
  font-size: 22px;
}

.member-role {
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: 10px;
}

.team-member p:last-child {
  margin-bottom: 20px;
}

/* Testimonials Section */
.testimonials-section {
  background-color: var(--secondary-color);
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.testimonial-card {
  background-color: var(--neutral-secondary);
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 152, 169, 0.1);
  color: var(--light-color);
  transition: var(--transition);
}

.testimonial-card:hover {
  transform: translateY(-5px);
  border-color: rgba(255, 152, 169, 0.2);
}

.testimonial-content {
  position: relative;
  padding: 20px 0;
}

.testimonial-content::before {
  content: '"';
  font-size: 80px;
  position: absolute;
  top: -20px;
  left: -15px;
  color: rgba(52, 152, 219, 0.1);
  font-family: Georgia, serif;
}

.testimonial-author h4 {
  margin-bottom: 5px;
  color: var(--dark-color);
}

.testimonial-author p {
  color: var(--text-light);
  font-style: italic;
}

/* Contact Section */
.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.contact-item i {
  font-size: 24px;
  color: var(--primary-color);
}

.form-group {
  margin-bottom: 20px;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid rgba(255, 152, 169, 0.2);
  border-radius: 5px;
  font-size: 16px;
  transition: var(--transition);
  background-color: var(--secondary-color);
  color: var(--light-color);
}

.form-group input:focus,
.form-group textarea:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 152, 169, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* Footer */
.main-footer {
  background-color: var(--secondary-color);
  color: var(--light-color);
  padding: 30px 0;
  text-align: center;
  border-top: 1px solid rgba(255, 152, 169, 0.1);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .hero-content h1 {
    font-size: 36px;
  }

  section {
    padding: 80px 0;
  }
}

@media (max-width: 768px) {
  .menu-toggle {
    display: block;
  }

  .main-nav {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: var(--secondary-color);
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    clip-path: polygon(0 0, 100% 0, 100% 0, 0 0);
    transition: var(--transition);
    border-top: 1px solid rgba(255, 152, 169, 0.1);
  }

  .main-nav.active {
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
  }

  .main-nav ul {
    flex-direction: column;
  }

  .main-nav ul li {
    margin: 10px 0;
  }

  .hero-content h1 {
    font-size: 30px;
  }

  .section-header h2 {
    font-size: 28px;
  }
}
