body {
  margin: 0;
  font-family: 'Segoe UI', sans-serif;
  background: #111;
  color: #fff;
}

.login-body {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.login-container {
  background: #1e1e1e;
  padding: 2rem;
  border-radius: 10px;
  text-align: center;
}

.login-container input,
.login-container button {
  display: block;
  margin: 1rem auto;
  padding: 0.75rem;
  width: 100%;
  max-width: 300px;
}

.login-container button {
  background: #ff99aa;
  border: none;
  color: white;
  cursor: pointer;
  font-weight: bold;
  border-radius: 5px;
}

.error {
  color: #ff5555;
}

.dashboard-body {
  padding: 2rem;
  background: #111;
}

.grid-container {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
}

.text-section {
  flex: 1 1 40%;
  max-width: 500px;
}

.text-section h1 {
  font-size: 2.5rem;
}

.image-gallery {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  flex: 1 1 50%;
}

.image-gallery img {
  width: 100%;
  border-radius: 12px;
  object-fit: cover;
}

.logout-btn {
  display: inline-block;
  margin-top: 1rem;
  color: #ccc;
  font-size: 0.9rem;
}
