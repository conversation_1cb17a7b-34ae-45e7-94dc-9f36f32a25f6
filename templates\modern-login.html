<!DOCTYPE html>
<html lang="cs">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>strategy.co - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>í</title>
  <link rel="stylesheet" href="{{ url_for('static', filename='modern-login.css') }}">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="modern-login-body">
  <!-- Background Elements -->
  <div class="login-background">
    <div class="bg-shapes">
      <div class="shape shape-1"></div>
      <div class="shape shape-2"></div>
      <div class="shape shape-3"></div>
      <div class="shape shape-4"></div>
    </div>
    <div class="bg-grid"></div>
  </div>

  <!-- Login Container -->
  <div class="login-wrapper">
    <div class="login-card">
      <!-- Header -->
      <div class="login-header">
        <div class="brand-logo">
          <span class="brand-text">strategy.co</span>
        </div>
        <h1 class="login-title">Vstup pro členy</h1>
        <p class="login-subtitle">Přihlaste se do klubu strategických manažerů</p>
      </div>

      <!-- Error Message -->
      {% if error %}
      <div class="error-message" id="error-message">
        <div class="error-icon">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
        <p>{{ error }}</p>
      </div>
      {% endif %}

      <!-- Login Form -->
      <form class="modern-login-form" id="login-form">
        <!-- Email Input -->
        <div class="input-group">
          <label for="email" class="input-label">Emailová adresa</label>
          <div class="input-wrapper">
            <div class="input-icon">
              <i class="fas fa-envelope"></i>
            </div>
            <input
              type="email"
              id="email"
              name="email"
              placeholder="váš@email.cz"
              required
              class="modern-input"
              autocomplete="email"
            >
          </div>
        </div>

        <!-- Passcode Input -->
        <div class="input-group">
          <label class="input-label">Váš 6-místný kód</label>
          <div class="passcode-wrapper">
            <div class="passcode-inputs" id="passcode-container">
              <input type="text" name="digit1" maxlength="1" pattern="[0-9]" inputmode="numeric" class="passcode-digit" required>
              <input type="text" name="digit2" maxlength="1" pattern="[0-9]" inputmode="numeric" class="passcode-digit" required>
              <input type="text" name="digit3" maxlength="1" pattern="[0-9]" inputmode="numeric" class="passcode-digit" required>
              <input type="text" name="digit4" maxlength="1" pattern="[0-9]" inputmode="numeric" class="passcode-digit" required>
              <input type="text" name="digit5" maxlength="1" pattern="[0-9]" inputmode="numeric" class="passcode-digit" required>
              <input type="text" name="digit6" maxlength="1" pattern="[0-9]" inputmode="numeric" class="passcode-digit" required>
              <input type="hidden" name="password" id="combined-password">
            </div>
          </div>
        </div>

        <!-- Submission Indicator -->
        <div class="submission-indicator" id="submission-indicator">
          <div class="loading-spinner">
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
          </div>
          <p class="loading-text">Přihlašování...</p>
        </div>

        <!-- Submit Button -->
        <button type="submit" class="modern-submit-btn" id="submit-btn">
          <span class="btn-text">Přihlásit se</span>
          <div class="btn-icon">
            <i class="fas fa-arrow-right"></i>
          </div>
          <div class="btn-ripple"></div>
        </button>
      </form>

      <!-- Footer -->
      <div class="login-footer">
        <p class="footer-text">
          <i class="fas fa-shield-alt"></i>
          Zabezpečené přihlášení
        </p>
      </div>
    </div>

    <!-- Decorative Elements -->
    <div class="floating-elements">
      <div class="float-element element-1">
        <i class="fas fa-chart-line"></i>
      </div>
      <div class="float-element element-2">
        <i class="fas fa-users"></i>
      </div>
      <div class="float-element element-3">
        <i class="fas fa-lightbulb"></i>
      </div>
      <div class="float-element element-4">
        <i class="fas fa-target"></i>
      </div>
    </div>
  </div>

  <script>
    // Modern Login JavaScript with Micro-interactions
    document.addEventListener('DOMContentLoaded', function() {
      const passcodeInputs = document.querySelectorAll('.passcode-digit');
      const combinedPassword = document.getElementById('combined-password');
      const passcodeContainer = document.getElementById('passcode-container');
      const submissionIndicator = document.getElementById('submission-indicator');
      const submitBtn = document.getElementById('submit-btn');
      const errorMessage = document.getElementById('error-message');

      // Initialize animations
      initPageAnimations();
      initPasscodeLogic();
      initFormInteractions();

      // Check for error and trigger shake animation (from server-side rendering)
      if (errorMessage) {
        handleLoginError();
      }

      function initPageAnimations() {
        // Animate login card entrance
        const loginCard = document.querySelector('.login-card');
        loginCard.style.opacity = '0';
        loginCard.style.transform = 'translateY(30px) scale(0.95)';

        setTimeout(() => {
          loginCard.style.transition = 'all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
          loginCard.style.opacity = '1';
          loginCard.style.transform = 'translateY(0) scale(1)';
        }, 100);

        // Animate floating elements
        const floatingElements = document.querySelectorAll('.float-element');
        floatingElements.forEach((element, index) => {
          element.style.animationDelay = `${index * 0.5}s`;
        });
      }

      function initPasscodeLogic() {
        passcodeInputs.forEach((input, index) => {
          // Focus first input
          if (index === 0) {
            setTimeout(() => input.focus(), 500);
          }

          // Input event handling
          input.addEventListener('input', function(e) {
            this.value = this.value.replace(/[^0-9]/g, '');

            // Add input animation
            this.style.transform = 'scale(1.1)';
            setTimeout(() => {
              this.style.transform = '';
            }, 150);

            if (this.value.length === 1) {
              if (index < passcodeInputs.length - 1) {
                passcodeInputs[index + 1].focus();
              } else {
                // All digits entered
                updateCombinedPassword();
                checkAllDigitsFilled();
              }
            }

            updateCombinedPassword();
          });

          // Backspace handling
          input.addEventListener('keydown', function(e) {
            if (e.key === 'Backspace' && this.value === '' && index > 0) {
              passcodeInputs[index - 1].focus();
            }
          });

          // Paste handling
          input.addEventListener('paste', function(e) {
            e.preventDefault();
            const pastedData = (e.clipboardData || window.clipboardData).getData('text');
            if (/^\d+$/.test(pastedData)) {
              const digits = pastedData.split('');
              passcodeInputs.forEach((input, i) => {
                if (i < digits.length) {
                  input.value = digits[i];
                  // Add paste animation
                  input.style.transform = 'scale(1.1)';
                  setTimeout(() => {
                    input.style.transform = '';
                  }, 150);
                }
              });

              updateCombinedPassword();
              if (combinedPassword.value.length === 6) {
                showSubmissionEffect();
              }
            }
          });

          // Focus/blur effects
          input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
          });

          input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
          });
        });
      }

      function initFormInteractions() {
        // Email input animations
        const emailInput = document.getElementById('email');
        const emailWrapper = emailInput.closest('.input-wrapper');

        emailInput.addEventListener('focus', () => {
          emailWrapper.classList.add('focused');
        });

        emailInput.addEventListener('blur', () => {
          emailWrapper.classList.remove('focused');
        });

        // Submit button interactions
        submitBtn.addEventListener('mouseenter', () => {
          submitBtn.style.transform = 'translateY(-2px)';
        });

        submitBtn.addEventListener('mouseleave', () => {
          submitBtn.style.transform = '';
        });

        submitBtn.addEventListener('mousedown', () => {
          createRippleEffect(event);
        });
      }

      function updateCombinedPassword() {
        let combined = '';
        passcodeInputs.forEach(input => {
          combined += input.value;
        });
        combinedPassword.value = combined;
      }

      function checkAllDigitsFilled() {
        let allFilled = true;
        passcodeInputs.forEach(input => {
          if (input.value.length === 0) {
            allFilled = false;
          }
        });

        if (allFilled) {
          // Validate email is also filled
          const emailInput = document.getElementById('email');
          if (emailInput.value.trim() === '') {
            // Focus email if empty
            emailInput.focus();
            return;
          }

          // Validate email format
          if (!emailInput.validity.valid) {
            emailInput.focus();
            return;
          }

          showSubmissionEffect();
        }
      }

      function showSubmissionEffect() {
        // Prevent multiple submissions
        if (submissionIndicator.classList.contains('active')) {
          return;
        }

        // Just show loading indicator, no success colors yet
        submissionIndicator.classList.add('active');
        submitBtn.style.display = 'none';

        // Make inputs readonly to prevent changes during submission
        passcodeInputs.forEach(input => {
          input.setAttribute('readonly', true);
        });
        document.getElementById('email').setAttribute('readonly', true);

        // Submit via AJAX - no page reload!
        setTimeout(() => {
          submitFormAjax();
        }, 300);
      }

      function submitFormAjax() {
        const formData = new FormData();
        formData.append('email', document.getElementById('email').value);
        formData.append('password', combinedPassword.value);

        fetch('/login', {
          method: 'POST',
          body: formData
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            // Show success animation
            showSuccessAnimation();
          } else {
            // Show error and reset form
            showErrorMessage(data.message);
            resetFormAfterError();
          }
        })
        .catch(error => {
          console.error('Error:', error);
          showErrorMessage('Chyba při přihlašování.');
          resetFormAfterError();
        });
      }

      function handleLoginError() {
        // Clear passcode inputs
        passcodeInputs.forEach(input => {
          input.value = '';
          input.removeAttribute('readonly');
        });

        // Hide loading indicator and show button again
        submissionIndicator.classList.remove('active');
        submitBtn.style.display = 'flex';

        // Trigger shake animation
        setTimeout(() => {
          passcodeContainer.classList.add('shake');
          passcodeInputs.forEach(input => {
            input.classList.add('error');
          });

          // Remove animations after completion
          setTimeout(() => {
            passcodeContainer.classList.remove('shake');
          }, 600);

          setTimeout(() => {
            passcodeInputs.forEach(input => {
              input.classList.remove('error');
            });
          }, 2000);

          // Focus first input
          setTimeout(() => {
            passcodeInputs[0].focus();
          }, 700);
        }, 100);
      }

      // Function to show success animation (only called on successful login)
      function showSuccessAnimation() {
        passcodeInputs.forEach((input, index) => {
          setTimeout(() => {
            input.classList.add('success');
          }, index * 50);
        });

        // Update loading text
        const loadingText = document.querySelector('.loading-text');
        if (loadingText) {
          loadingText.textContent = 'Úspěšně přihlášen!';
        }

        // Redirect after showing success
        setTimeout(() => {
          window.location.href = '/index';
        }, 1500);
      }

      function showErrorMessage(message) {
        // Create or update error message
        let errorDiv = document.getElementById('dynamic-error');
        if (!errorDiv) {
          errorDiv = document.createElement('div');
          errorDiv.id = 'dynamic-error';
          errorDiv.className = 'error-message';
          errorDiv.innerHTML = `
            <div class="error-icon">
              <i class="fas fa-exclamation-triangle"></i>
            </div>
            <p>${message}</p>
          `;

          // Insert before the form
          const form = document.getElementById('login-form');
          form.parentNode.insertBefore(errorDiv, form);
        } else {
          errorDiv.querySelector('p').textContent = message;
        }

        // Animate error message
        errorDiv.style.opacity = '0';
        errorDiv.style.transform = 'translateY(-10px)';
        setTimeout(() => {
          errorDiv.style.transition = 'all 0.3s ease';
          errorDiv.style.opacity = '1';
          errorDiv.style.transform = 'translateY(0)';
        }, 50);
      }

      function resetFormAfterError() {
        // Hide loading indicator
        submissionIndicator.classList.remove('active');
        submitBtn.style.display = 'flex';

        // Clear and enable passcode inputs
        passcodeInputs.forEach(input => {
          input.value = '';
          input.removeAttribute('readonly');
          input.classList.remove('error');
        });

        // Enable email input
        document.getElementById('email').removeAttribute('readonly');

        // Trigger shake animation
        setTimeout(() => {
          passcodeContainer.classList.add('shake');
          passcodeInputs.forEach(input => {
            input.classList.add('error');
          });

          // Remove animations after completion
          setTimeout(() => {
            passcodeContainer.classList.remove('shake');
          }, 600);

          setTimeout(() => {
            passcodeInputs.forEach(input => {
              input.classList.remove('error');
            });
          }, 2000);

          // Focus first input
          setTimeout(() => {
            passcodeInputs[0].focus();
          }, 700);
        }, 100);
      }

      function createRippleEffect(e) {
        const ripple = submitBtn.querySelector('.btn-ripple');
        const rect = submitBtn.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;

        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('active');

        setTimeout(() => {
          ripple.classList.remove('active');
        }, 600);
      }

      // Form submission validation
      document.getElementById('login-form').addEventListener('submit', function(e) {
        e.preventDefault(); // Always prevent default form submission

        updateCombinedPassword();

        // Validate email
        const emailInput = document.getElementById('email');
        if (emailInput.value.trim() === '' || !emailInput.validity.valid) {
          emailInput.focus();

          // Add error styling to email
          emailInput.closest('.input-wrapper').classList.add('error');
          setTimeout(() => {
            emailInput.closest('.input-wrapper').classList.remove('error');
          }, 2000);
          return;
        }

        // Validate passcode
        if (combinedPassword.value.length !== 6) {
          // Shake animation for incomplete passcode
          passcodeContainer.classList.add('shake');
          setTimeout(() => {
            passcodeContainer.classList.remove('shake');
          }, 600);

          // Focus first empty input
          for (let i = 0; i < passcodeInputs.length; i++) {
            if (!passcodeInputs[i].value) {
              passcodeInputs[i].focus();
              break;
            }
          }
          return;
        }

        // If we get here, form is valid - show loading and submit via AJAX
        showSubmissionEffect();
      });
    });
  </script>
</body>
</html>
